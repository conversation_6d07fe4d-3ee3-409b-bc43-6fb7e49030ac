"use client";

import React from "react";

export function Stats27() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 max-w-lg md:mb-18 lg:mb-20">
          <h2 className="mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            Wat zegt de wetenschap?
          </h2>
        </div>
        <div className="grid grid-cols-1 gap-y-8 lg:grid-cols-3 lg:gap-x-8 lg:gap-y-12">
          <div className="p-8 border rounded-xl">
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Productiever
            </h3>
            <p className="mt-2">
              Werknemers die regelmatig in contact komen met natuur zijn zeker
              15% productiever en ervaren minder stress. Groene omgevingen
              verbeteren cognitieve functies en het concentratievermogen.
            </p>
          </div>
          <div className="p-8">
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Teamgeest en werkplezier
            </h3>
            <p className="mt-2">
              Deelname aan collectieve natuurprojecten zorgt voor meer
              verbondenheid en betrokkenheid onder collega’s. Groene
              teambuildings zorgen voor meer teamgeest en werkplezier.
            </p>
          </div>
          <div className="p-8">
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Planten en dieren
            </h3>
            <p className="mt-2">
              Kleinschalige bossen en groenstroken in steden functioneren als
              ‘stepping stones’ voor dieren en planten, waardoor soorten zich
              beter kunnen verspreiden. Onderzoek toont aan dat stedelijke
              bossen de diversiteit aan vogels, insecten en zoogdieren
              aanzienlijk verhogen, met alle positieven gevolgen van dien voor
              ons allemaal.
            </p>
          </div>
        </div>
        <div className="mt-10 flex flex-wrap items-center gap-4 md:mt-14 lg:mt-16" />
      </div>
    </section>
  );
}
